# Environment Variables for TPQ Wali Santri Mobile App
# Copy this file to .env and fill in your actual values

# API Configuration
API_BASE_URL=http://localhost:3000/api
API_TIMEOUT=10000

# App Configuration
APP_ENV=development
APP_VERSION=1.0.0
APP_BUILD_NUMBER=1

# Authentication
JWT_SECRET=your-jwt-secret-here
REFRESH_TOKEN_EXPIRY=7d

# Database (if needed for local development)
DATABASE_URL=mysql://root:admin123@localhost:3306/db_tpq

# External Services
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_PROJECT_ID=your-firebase-project-id

# Push Notifications
EXPO_PUSH_TOKEN=your-expo-push-token
FCM_SERVER_KEY=your-fcm-server-key

# Analytics
GOOGLE_ANALYTICS_ID=your-ga-id
MIXPANEL_TOKEN=your-mixpanel-token

# Error Tracking
SENTRY_DSN=your-sentry-dsn

# Payment Gateway
MIDTRANS_CLIENT_KEY=your-midtrans-client-key
MIDTRANS_SERVER_KEY=your-midtrans-server-key

# Social Media
FACEBOOK_APP_ID=your-facebook-app-id
GOOGLE_CLIENT_ID=your-google-client-id

# Feature Flags
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=true
ENABLE_CRASH_REPORTING=true
ENABLE_OFFLINE_MODE=true

# Debug Settings
DEBUG_MODE=true
LOG_LEVEL=debug
ENABLE_FLIPPER=true
