# Assets Directory

This directory contains all the assets required for building the mobile app.

## Required Assets

### Icons

- **icon.png**: App icon (1024x1024px) (1024x1024)
- **adaptive-icon.png**: Android adaptive icon foreground (1024x1024px) (1024x1024)
- **splash.png**: Splash screen image (1284x2778px for iPhone 12 Pro Max) (1284x2778)
- **favicon.png**: Web favicon (48x48px) (48x48)
- **notification-icon.png**: Notification icon (96x96px) (96x96)

### Fonts

- **Inter-Regular.ttf**: Inter Regular font (Weight: 400)
- **Inter-Medium.ttf**: Inter Medium font (Weight: 500)
- **Inter-SemiBold.ttf**: Inter SemiBold font (Weight: 600)
- **Inter-Bold.ttf**: Inter Bold font (Weight: 700)

## Asset Guidelines

### App Icon (icon.png)

- Size: 1024x1024px
- Format: PNG with transparency
- Should be simple and recognizable at small sizes
- Avoid text that might be hard to read

### Adaptive Icon (adaptive-icon.png)

- Size: 1024x1024px
- Format: PNG with transparency
- Android adaptive icon foreground layer
- Should work well with different background shapes

### Splash Screen (splash.png)

- Size: 1284x2778px (iPhone 12 Pro Max resolution)
- Format: PNG
- Will be scaled for different screen sizes
- Keep important content in the center

### Fonts

- Download Inter fonts from: https://fonts.google.com/specimen/Inter
- Place TTF files in the fonts subdirectory
- Ensure proper licensing for commercial use

## Generating Assets

You can use online tools or design software to create these assets:

1. **Figma**: Free design tool with export capabilities
2. **Canva**: Easy-to-use design platform
3. **Adobe Illustrator/Photoshop**: Professional design tools
4. **GIMP**: Free alternative to Photoshop

## Asset Optimization

Before building, ensure your assets are optimized:

- Use PNG for icons and images with transparency
- Compress images without losing quality
- Test icons at different sizes to ensure clarity
