{"name": "rumah-<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "type": "module", "prisma": {"seed": "node prisma/seed-simple.js"}, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "node --loader ts-node/esm prisma/seed.ts", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:keploy": "jest --coverage --config='{\"testEnvironment\":\"jsdom\",\"transform\":{\"^.+\\\\.(js|jsx|ts|tsx)$\":[\"@swc/jest\",{\"jsc\":{\"parser\":{\"syntax\":\"typescript\",\"tsx\":true,\"dynamicImport\":true},\"transform\":{\"react\":{\"runtime\":\"automatic\"}}},\"module\":{\"type\":\"commonjs\"}}]},\"moduleFileExtensions\":[\"js\",\"jsx\",\"ts\",\"tsx\"],\"coveragePathIgnorePatterns\":[\"/monaco-editor/\"],\"collectCoverageFrom\":[\"./**/*.{js,jsx,ts,tsx}\"],\"coverageDirectory\":\".\",\"coverageReporters\":[\"text\",\"cobertura\"]}'"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.10.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.22.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@react-native-community/masked-view": "^0.1.11", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@types/dom-mediacapture-record": "^1.0.22", "@types/html2canvas": "^0.5.35", "@types/jsonwebtoken": "^9.0.9", "@types/jspdf": "^1.3.3", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.17", "@types/qrcode": "^1.5.5", "@types/socket.io-client": "^1.4.36", "axios": "^1.9.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.454.0", "midtrans-client": "^1.4.3", "multer": "^2.0.0", "mysql2": "^3.14.2", "next": "15.3.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "qr-scanner": "^1.4.2", "qrcode": "^1.5.4", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "react-native-gesture-handler": "^2.26.0", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "sharp": "^0.34.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "twilio": "^5.7.0", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.31.0", "@next/eslint-plugin-next": "^15.3.5", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-toastify": "^4.0.2", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.35", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "bcryptjs": "^3.0.2", "eslint": "^9.30.1", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "jest": "^30.0.5", "prettier": "^3.6.2", "prisma": "^5.22.0", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5", "typescript-eslint": "^8.36.0", "vitest": "^3.2.4"}}