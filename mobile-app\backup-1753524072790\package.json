{"name": "tpq-wali-app", "version": "1.0.0", "description": "TPQ Bai<PERSON> - Aplikasi Wali <PERSON>ri", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:apk": "node scripts/build-apk.js", "build:preview": "eas build --platform android --profile preview", "build:production": "eas build --platform android --profile production", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "setup:build": "node scripts/setup-build.js", "generate:assets": "node scripts/generate-assets.js", "build:status": "eas build:list", "prebuild": "expo prebuild --clean", "env:create": "node scripts/manage-env.js create", "env:validate": "node scripts/manage-env.js validate", "env:configure": "node scripts/manage-env.js configure", "env:status": "node scripts/manage-env.js status", "upgrade:sdk": "node scripts/upgrade-sdk.js"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "axios": "^1.6.5", "date-fns": "^3.2.0", "expo": "^53.0.0", "expo-blur": "~12.9.2", "expo-device": "^7.1.4", "expo-font": "~11.10.3", "expo-haptics": "~12.8.1", "expo-linear-gradient": "~12.7.2", "expo-linking": "~6.2.2", "expo-notifications": "~0.27.6", "expo-secure-store": "~12.8.1", "expo-splash-screen": "~0.26.4", "expo-status-bar": "~1.11.1", "expo-web-browser": "~12.8.2", "lottie-react-native": "^6.5.1", "react": "18.2.0", "react-hook-form": "^7.49.3", "react-native": "0.73.4", "react-native-chart-kit": "^6.12.0", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "~2.14.0", "react-native-modal": "^13.0.1", "react-native-paper": "^5.12.3", "react-native-progress": "^5.0.1", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-skeleton-placeholder": "^5.2.4", "react-native-super-grid": "^5.0.0", "react-native-svg": "14.1.0", "react-native-vector-icons": "^10.0.3", "react-query": "^3.39.3", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "@types/react-native": "~0.73.0", "typescript": "^5.1.3"}, "private": true}