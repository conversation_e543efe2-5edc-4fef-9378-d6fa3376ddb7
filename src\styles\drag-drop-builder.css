/* Drag & Drop Builder Specific Styles */

.drag-drop-builder-container {
  height: calc(100vh - 180px);
  overflow: hidden;
}

.drag-drop-builder-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drag-drop-builder-tabs .tabs-content {
  flex: 1;
  overflow: hidden;
}

.drag-drop-builder-main {
  height: 100%;
  display: flex;
  overflow: hidden;
}

.drag-drop-builder-sidebar {
  width: 256px;
  height: 100%;
  overflow-y: auto;
  border-right: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.drag-drop-builder-canvas {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.drag-drop-builder-toolbar {
  border-bottom: 1px solid #e5e7eb;
  background-color: white;
  padding: 16px;
  flex-shrink: 0;
}

.drag-drop-builder-canvas-area {
  flex: 1;
  background-color: #f3f4f6;
  padding: 16px;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drag-drop-builder-property-panel {
  width: 320px;
  height: 100%;
  border-left: 1px solid #e5e7eb;
  background-color: #f9fafb;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.drag-drop-builder-property-header {
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background-color: white;
  flex-shrink: 0;
}

.drag-drop-builder-property-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.drag-drop-builder-property-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  background-color: white;
  flex-shrink: 0;
}

/* Mobile Frame Styles */
.mobile-frame {
  background: linear-gradient(145deg, #1f2937, #111827);
  border-radius: 24px;
  padding: 20px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.mobile-screen {
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
}

.mobile-status-bar {
  background-color: #000000;
  color: white;
  font-size: 12px;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mobile-content {
  padding: 16px;
  overflow-y: auto;
}

/* Drag & Drop Styles */
.draggable-component {
  cursor: grab;
  transition: all 0.2s ease;
}

.draggable-component:hover {
  background-color: #f3f4f6;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.draggable-component:active {
  cursor: grabbing;
}

.droppable-area {
  min-height: 400px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.droppable-area.drag-over {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.component-selected {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  background-color: #eff6ff;
}

.component-hover {
  outline: 1px solid #9ca3af;
  outline-offset: 1px;
}

/* Animation Styles */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Modal Styles */
.modal-overlay {
  backdrop-filter: blur(4px);
}

.modal-content {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    transform: scale(0.95) translateY(-10px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .drag-drop-builder-sidebar {
    width: 200px;
  }

  .drag-drop-builder-property-panel {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .drag-drop-builder-main {
    flex-direction: column;
  }

  .drag-drop-builder-sidebar,
  .drag-drop-builder-property-panel {
    width: 100%;
    height: auto;
    max-height: 200px;
  }

  .drag-drop-builder-canvas {
    height: 400px;
  }
}

/* Scrollbar Styles */
.drag-drop-builder-sidebar::-webkit-scrollbar,
.drag-drop-builder-property-content::-webkit-scrollbar {
  width: 6px;
}

.drag-drop-builder-sidebar::-webkit-scrollbar-track,
.drag-drop-builder-property-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.drag-drop-builder-sidebar::-webkit-scrollbar-thumb,
.drag-drop-builder-property-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.drag-drop-builder-sidebar::-webkit-scrollbar-thumb:hover,
.drag-drop-builder-property-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus Styles */
.drag-drop-builder-container *:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading Styles */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Tooltip Styles */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #1f2937;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}
