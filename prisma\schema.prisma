generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                    String                 @id @default(cuid())
  email                 String                 @unique
  name                  String
  phone                 String?
  role                  String
  password              String
  avatar                String?
  isActive              Boolean                @default(true)
  status                String                 @default("ACTIVE") // Added for compatibility
  halaqahId             String?                // Added for musyrif assignment
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  attendanceAsMusyrif   Attendance[]           @relation("MusyrifAttendance")
  musyrifAttendance     MusyrifAttendance[]    @relation("MusyrifAttendanceRecord")
  auditLogs             AuditLog[]
  cartItems             CartItem[]
  productCartItems      ProductCartItem[]      @relation("ProductCartItems")
  donationsConfirmed    Donation[]             @relation("DonationConfirmer")
  emailMessages         EmailMessage[]
  emailTemplates        EmailTemplate[]
  reportsGenerated      FinancialReport[]      @relation("ReportGenerator")
  hafalanAsMusyrif      <PERSON>[]              @relation("MusyrifHafalan")
  musyrif               Musyrif[]
  notificationTemplates NotificationTemplate[]
  notificationsCreated  Notification[]         @relation("NotificationCreator")
  notifications         Notification[]
  orders                Order[]
  paymentTransactions   PaymentTransaction[]
  santriAsWali          Santri[]               @relation("WaliSantri")
  subscriptions         Subscription[]
  testimonials          Testimonial[]          @relation("Testimonials")
  themes                Theme[]
  transactionsCreated   Transaction[]          @relation("TransactionCreator")
  whatsappIncoming      WhatsAppIncoming[]     @relation("WhatsAppIncoming")
  whatsappLogs          WhatsAppLog[]          @relation("WhatsAppLogs")
  whatsappMessages      WhatsAppMessage[]
  behaviorRecords       Behavior[]             @relation("BehaviorRecorder")
  campaignsCreated      DonationCampaign[]     @relation("CampaignCreator")
  campaignUpdatesCreated CampaignUpdate[]      @relation("CampaignUpdateCreator")

  // Salary System Relations
  earningsApproved      MusyrifEarning[]       @relation("EarningApprover")
  withdrawalsApproved   MusyrifWithdrawal[]    @relation("WithdrawalApprover")

  @@map("users")
}

model Santri {
  id              String            @id @default(cuid())
  nis             String            @unique
  name            String
  birthDate       DateTime
  birthPlace      String
  gender          String
  address         String
  phone           String?
  email           String?
  photo           String?
  status          String            @default("ACTIVE")
  enrollmentDate  DateTime          @default(now())
  graduationDate  DateTime?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  waliId          String
  halaqahId       String?
  attendance      Attendance[]
  hafalan         Hafalan[]
  hafalanProgress HafalanProgress[] @relation("SantriHafalanProgress")
  payments        Payment[]
  halaqah         Halaqah?          @relation("SantriHalaqah", fields: [halaqahId], references: [id])
  wali            User              @relation("WaliSantri", fields: [waliId], references: [id])
  sppRecords      SPPRecord[]       @relation("SantriSPP")
  testimonials    Testimonial[]
  transactions    Transaction[]     @relation("SantriTransactions")
  achievements    SantriAchievement[]
  behaviorRecords Behavior[]

  @@index([halaqahId], map: "santri_halaqahId_fkey")
  @@index([waliId], map: "santri_waliId_fkey")
  @@map("santri")
}

model Musyrif {
  id               String   @id @default(cuid())
  name             String
  gender           String
  birthDate        DateTime
  birthPlace       String
  address          String
  phone            String
  email            String
  specialization   String?
  joinDate         DateTime @default(now())
  status           String   @default("ACTIVE")
  photo            String?
  educationData    String?
  experienceData   String?
  certificatesData String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  userId           String?
  halaqahId        String?  @unique
  halaqah          Halaqah? @relation("MusyrifHalaqah")
  user             User?    @relation(fields: [userId], references: [id])

  // Salary & Wallet Relations
  salaryRates      MusyrifSalaryRate[] @relation("MusyrifSalaryRates")
  wallet           MusyrifWallet?      @relation("MusyrifWallet")
  earnings         MusyrifEarning[]    @relation("MusyrifEarnings")
  withdrawals      MusyrifWithdrawal[] @relation("MusyrifWithdrawals")

  @@index([userId], map: "musyrif_userId_fkey")
  @@map("musyrif")
}

model Halaqah {
  id          String            @id @default(cuid())
  name        String
  description String?
  capacity    Int               @default(20)
  level       String            @default("BEGINNER")
  room        String?
  schedule    String?           // JSON string for schedule data
  status      String            @default("ACTIVE")
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  musyrifId   String?           @unique
  attendance  Attendance[]
  musyrifAttendance MusyrifAttendance[] @relation("MusyrifAttendanceHalaqah")
  qrCodeSessions QRCodeSession[] @relation("QRCodeSessions")
  musyrif     Musyrif?          @relation("MusyrifHalaqah", fields: [musyrifId], references: [id])
  schedules   HalaqahSchedule[]
  santri      Santri[]          @relation("SantriHalaqah")
  behaviorRecords Behavior[]

  @@map("halaqah")
}

model HalaqahSchedule {
  id        String   @id @default(cuid())
  dayOfWeek Int
  startTime String
  endTime   String
  room      String   @default("")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  halaqahId String
  halaqah   Halaqah  @relation(fields: [halaqahId], references: [id], onDelete: Cascade)

  @@index([halaqahId], map: "halaqah_schedules_halaqahId_fkey")
  @@map("halaqah_schedules")
}

model Hafalan {
  id         String   @id @default(cuid())
  surahId    Int
  surahName  String
  ayahStart  Int
  ayahEnd    Int
  type       String
  status     String   @default("PENDING")
  grade      Int?
  notes      String?
  recordedAt DateTime @default(now())
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  santriId   String
  musyrifId  String
  audioUrl   String?
  musyrif    User     @relation("MusyrifHafalan", fields: [musyrifId], references: [id])
  santri     Santri   @relation(fields: [santriId], references: [id], onDelete: Cascade)

  @@index([musyrifId], map: "hafalan_musyrifId_fkey")
  @@index([santriId], map: "hafalan_santriId_fkey")
  @@map("hafalan")
}

model HafalanProgress {
  id          String    @id @default(cuid())
  santriId    String
  surahId     Int
  surahName   String
  totalAyah   Int
  memorized   Int       @default(0)
  inProgress  Int       @default(0)
  lastAyah    Int       @default(0)
  startDate   DateTime  @default(now())
  targetDate  DateTime?
  completedAt DateTime?
  status      String    @default("IN_PROGRESS")
  notes       String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  santri      Santri    @relation("SantriHafalanProgress", fields: [santriId], references: [id], onDelete: Cascade)

  @@unique([santriId, surahId])
  @@index([santriId], map: "hafalan_progress_santriId_fkey")
  @@map("hafalan_progress")
}

model Attendance {
  id           String    @id @default(cuid())
  date         DateTime
  status       String
  checkInTime  DateTime?
  checkOutTime DateTime?
  notes        String?
  latitude     Float?
  longitude    Float?
  photo        String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  santriId     String
  halaqahId    String
  musyrifId    String
  halaqah      Halaqah   @relation(fields: [halaqahId], references: [id])
  musyrif      User      @relation("MusyrifAttendance", fields: [musyrifId], references: [id])
  santri       Santri    @relation(fields: [santriId], references: [id], onDelete: Cascade)

  @@unique([santriId, halaqahId, date])
  @@index([halaqahId], map: "attendance_halaqahId_fkey")
  @@index([musyrifId], map: "attendance_musyrifId_fkey")
  @@map("attendance")
}

model MusyrifAttendance {
  id           String    @id @default(cuid())
  date         DateTime
  status       String    // PRESENT, ABSENT, LATE
  checkInTime  DateTime?
  checkOutTime DateTime?
  notes        String?
  sessionType  String    @default("REGULAR") // REGULAR, EXTRA, MAKEUP
  qrCodeUsed   String?   // QR code that was scanned
  latitude     Float?
  longitude    Float?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  musyrifId    String
  halaqahId    String
  halaqah      Halaqah   @relation("MusyrifAttendanceHalaqah", fields: [halaqahId], references: [id])
  musyrif      User      @relation("MusyrifAttendanceRecord", fields: [musyrifId], references: [id])

  // Salary Relations
  earnings     MusyrifEarning[] @relation("AttendanceEarnings")

  @@unique([musyrifId, halaqahId, date])
  @@index([halaqahId])
  @@index([musyrifId])
  @@map("musyrif_attendance")
}

model QRCodeSession {
  id          String    @id @default(cuid())
  halaqahId   String
  sessionDate DateTime
  sessionType String    @default("REGULAR")
  qrCode      String    @unique
  isActive    Boolean   @default(true)
  expiresAt   DateTime
  createdBy   String
  usageCount  Int       @default(0)
  maxUsage    Int       @default(1)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  halaqah     Halaqah   @relation("QRCodeSessions", fields: [halaqahId], references: [id])

  @@index([halaqahId])
  @@index([qrCode])
  @@map("qr_code_sessions")
}

model Payment {
  id            String        @id @default(cuid())
  type          String
  amount        Int
  dueDate       DateTime
  paidDate      DateTime?
  status        String        @default("PENDING")
  method        String?
  reference     String?
  notes         String?
  midtransToken String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  santriId      String
  santri        Santri        @relation(fields: [santriId], references: [id], onDelete: Cascade)
  transactions  Transaction[]

  @@index([santriId], map: "payments_santriId_fkey")
  @@map("payments")
}

model SPPSetting {
  id          String      @id @default(cuid())
  name        String
  amount      Float
  description String?
  isActive    Boolean     @default(true)
  level       String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  sppRecords  SPPRecord[]

  @@map("spp_settings")
}

model SPPRecord {
  id            String       @id @default(cuid())
  month         Int
  year          Int
  amount        Float
  dueDate       DateTime
  paidDate      DateTime?
  status        String       @default("PENDING")
  paidAmount    Float        @default(0)
  discount      Float        @default(0)
  fine          Float        @default(0)
  notes         String?
  paymentMethod String?
  receiptNumber String?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  santriId      String
  sppSettingId  String
  transactionId String?      @unique
  santri        Santri       @relation("SantriSPP", fields: [santriId], references: [id], onDelete: Cascade)
  sppSetting    SPPSetting   @relation(fields: [sppSettingId], references: [id])
  transaction   Transaction? @relation("SPPTransaction", fields: [transactionId], references: [id])

  @@unique([santriId, month, year])
  @@index([sppSettingId], map: "spp_records_sppSettingId_fkey")
  @@map("spp_records")
}

model AuditLog {
  id        String   @id @default(cuid())
  action    String
  entity    String
  entityId  String
  oldData   String?
  newData   String?
  createdAt DateTime @default(now())
  userId    String
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId], map: "audit_logs_userId_fkey")
  @@map("audit_logs")
}

model News {
  id          String    @id @default(cuid())
  title       String
  excerpt     String
  content     String
  image       String?
  author      String
  category    String
  status      String    @default("DRAFT")
  featured    Boolean   @default(false)
  views       Int       @default(0)
  publishedAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("news")
}

model WhatsAppLog {
  id          String    @id @default(cuid())
  recipientId String
  messageType String
  messageData String
  messageId   String
  status      String    @default("PENDING")
  sentAt      DateTime  @default(now())
  deliveredAt DateTime?
  readAt      DateTime?
  failedAt    DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  recipient   User      @relation("WhatsAppLogs", fields: [recipientId], references: [id], onDelete: Cascade)

  @@index([recipientId], map: "whatsapp_logs_recipientId_fkey")
  @@map("whatsapp_logs")
}

model WhatsAppIncoming {
  id             String    @id @default(cuid())
  senderId       String
  senderPhone    String
  messageType    String
  messageContent String
  receivedAt     DateTime
  processed      Boolean   @default(false)
  processedAt    DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  sender         User      @relation("WhatsAppIncoming", fields: [senderId], references: [id], onDelete: Cascade)

  @@index([senderId], map: "whatsapp_incoming_senderId_fkey")
  @@map("whatsapp_incoming")
}

model EmailLog {
  id        String   @id @default(cuid())
  recipient String
  subject   String
  status    String   @default("PENDING")
  messageId String
  template  String   @default("")
  error     String   @default("")
  sentAt    DateTime @default(now())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("email_logs")
}

model Program {
  id          String   @id @default(cuid())
  title       String
  description String
  features    String
  duration    String
  ageGroup    String
  schedule    String
  price       String
  image       String?
  isActive    Boolean  @default(true)
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("programs")
}

model Testimonial {
  id         String   @id @default(cuid())
  content    String
  rating     Int      @default(5)
  isApproved Boolean  @default(false)
  isFeatured Boolean  @default(false)
  santriId   String?
  waliId     String?
  authorName String
  authorRole String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  santri     Santri?  @relation(fields: [santriId], references: [id])
  wali       User?    @relation("Testimonials", fields: [waliId], references: [id])

  @@index([santriId], map: "testimonials_santriId_fkey")
  @@index([waliId], map: "testimonials_waliId_fkey")
  @@map("testimonials")
}

model AchievementBadge {
  id                String              @id @default(cuid())
  name              String
  nameArabic        String
  description       String
  icon              String
  color             String
  category          String
  criteriaType      String
  criteriaValue     Int
  criteriaCondition String
  timeframe         String?
  rarity            String
  points            Int
  isActive          Boolean             @default(true)
  unlockMessage     String
  shareMessage      String
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  santriAchievements SantriAchievement[]

  @@map("achievement_badges")
}

model SantriAchievement {
  id                String           @id @default(cuid())
  santriId          String
  badgeId           String
  awardedAt         DateTime         @default(now())
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  badge             AchievementBadge @relation(fields: [badgeId], references: [id])
  santri            Santri           @relation(fields: [santriId], references: [id], onDelete: Cascade)

  @@unique([santriId, badgeId])
  @@index([badgeId], map: "santri_achievements_badgeId_fkey")
  @@map("santri_achievements")
}

model WhatsAppMessage {
  id          String   @id @default(cuid())
  userId      String
  messageType String
  content     String
  status      String   @default("PENDING")
  sentAt      DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "whatsapp_messages_userId_fkey")
  @@map("whatsapp_messages")
}

model EmailMessage {
  id        String   @id @default(cuid())
  userId    String
  subject   String
  content   String
  status    String   @default("PENDING")
  sentAt    DateTime @default(now())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "email_messages_userId_fkey")
  @@map("email_messages")
}

model EmailTemplate {
  id          String   @id @default(cuid())
  name        String
  subject     String
  content     String
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById String
  createdBy   User     @relation(fields: [createdById], references: [id])

  @@index([createdById], map: "email_templates_createdById_fkey")
  @@map("email_templates")
}

model NotificationTemplate {
  id          String   @id @default(cuid())
  name        String
  title       String
  content     String
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById String
  createdBy   User     @relation(fields: [createdById], references: [id])

  @@index([createdById], map: "notification_templates_createdById_fkey")
  @@map("notification_templates")
}

model Notification {
  id        String    @id @default(cuid())
  userId    String
  title     String
  message   String    @db.Text // Changed from content to message and made it TEXT
  type      String?   // Added type field for notification categorization
  relatedId String?   // Added relatedId for linking to related records
  metadata  String?   @db.Text // Added metadata field for additional data
  isRead    Boolean   @default(false)
  readAt    DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  createdBy String?   // Made optional since system can create notifications
  creator   User?     @relation("NotificationCreator", fields: [createdBy], references: [id])
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([createdBy], map: "notifications_createdBy_fkey")
  @@index([userId], map: "notifications_userId_fkey")
  @@index([userId, isRead], map: "notifications_userId_isRead_idx")
  @@map("notifications")
}

model FinancialAccount {
  id           String        @id @default(cuid())
  name         String
  accountType  String
  balance      Float         @default(0)
  description  String?
  isActive     Boolean       @default(true)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  transactions Transaction[]

  @@map("financial_accounts")
}

model Transaction {
  id                String          @id @default(cuid())
  transactionType   String
  amount            Float
  description       String?
  date              DateTime
  reference         String?
  status            String          @default("COMPLETED")
  paymentMethod     String?         // GATEWAY, BANK_TRANSFER, CASH
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  accountId         String
  createdById       String
  santriId          String?
  paymentId         String?
  paymentGatewayId  String?         // Reference to PaymentGateway
  bankAccountId     String?         // Reference to BankAccount for manual transfers
  account           FinancialAccount @relation(fields: [accountId], references: [id])
  createdBy         User            @relation("TransactionCreator", fields: [createdById], references: [id])
  payment           Payment?        @relation(fields: [paymentId], references: [id])
  santri            Santri?         @relation("SantriTransactions", fields: [santriId], references: [id])
  sppRecord         SPPRecord?      @relation("SPPTransaction")
  paymentGateway    PaymentGateway? @relation("PaymentGatewayTransactions", fields: [paymentGatewayId], references: [id])
  bankAccount       BankAccount?    @relation("BankAccountTransactions", fields: [bankAccountId], references: [id])

  @@index([accountId], map: "transactions_accountId_fkey")
  @@index([createdById], map: "transactions_createdById_fkey")
  @@index([paymentId], map: "transactions_paymentId_fkey")
  @@index([santriId], map: "transactions_santriId_fkey")
  @@index([paymentGatewayId], map: "transactions_paymentGatewayId_fkey")
  @@index([bankAccountId], map: "transactions_bankAccountId_fkey")
  @@map("transactions")
}

model FinancialReport {
  id          String   @id @default(cuid())
  reportType  String
  period      String
  startDate   DateTime
  endDate     DateTime
  data        String
  summary     String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  generatedBy String
  generator   User     @relation("ReportGenerator", fields: [generatedBy], references: [id])

  @@index([generatedBy], map: "financial_reports_generatedBy_fkey")
  @@map("financial_reports")
}

model Product {
  id          String     @id @default(cuid())
  name        String
  description String
  price       Float
  stock       Int        @default(0)
  image       String?
  category    String
  isActive    Boolean    @default(true)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  cartItems   ProductCartItem[]
  orderItems  OrderItem[]

  @@map("products")
}

model CartItem {
  id          String   @id @default(cuid())
  cartId      String
  userId      String?
  itemType    String   // SPP, DONATION, PRODUCT, etc.
  itemId      String   // Reference to the actual item
  name        String
  description String?
  price       Float
  quantity    Int
  metadata    String?  // JSON string for additional data
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Optional relations
  user        User?    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([cartId])
  @@index([userId])
  @@index([itemType, itemId])
  @@map("cart_items")
}

// Keep the old CartItem as ProductCartItem for e-commerce
model ProductCartItem {
  id        String   @id @default(cuid())
  quantity  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String
  productId String
  product   Product  @relation(fields: [productId], references: [id])
  user      User     @relation("ProductCartItems", fields: [userId], references: [id], onDelete: Cascade)

  @@index([productId], map: "product_cart_items_productId_fkey")
  @@index([userId], map: "product_cart_items_userId_fkey")
  @@map("product_cart_items")
}

model Order {
  id              String          @id @default(cuid())
  orderNumber     String          @unique
  status          String          @default("PENDING")
  totalAmount     Float
  notes           String?
  shippingAddress String?
  paymentMethod   String?
  paymentStatus   String          @default("PENDING")
  paidAt          DateTime?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  userId          String
  items           OrderItem[]
  transactions    PaymentTransaction[]
  user            User            @relation(fields: [userId], references: [id])

  @@index([userId], map: "orders_userId_fkey")
  @@map("orders")
}

model OrderItem {
  id        String   @id @default(cuid())
  quantity  Int
  price     Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  orderId   String
  productId String
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id])

  @@index([orderId], map: "order_items_orderId_fkey")
  @@index([productId], map: "order_items_productId_fkey")
  @@map("order_items")
}

model PaymentTransaction {
  id            String   @id @default(cuid())
  amount        Float
  paymentMethod String
  status        String   @default("PENDING")
  reference     String?
  notes         String?
  paidAt        DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  orderId       String
  userId        String
  order         Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  user          User     @relation(fields: [userId], references: [id])

  @@index([orderId], map: "payment_transactions_orderId_fkey")
  @@index([userId], map: "payment_transactions_userId_fkey")
  @@map("payment_transactions")
}

model DonationCategory {
  id          String            @id @default(cuid())
  title       String            @map("name")
  slug        String            @unique
  description String?
  target      Int               @default(0)
  collected   Int               @default(0)
  icon        String?
  color       String?
  bgColor     String?
  urgent      Boolean           @default(false)
  isActive    Boolean           @default(true)
  order       Int               @default(0)
  image       String?           // Featured image for category
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  donations   Donation[]
  campaigns   DonationCampaign[]

  @@map("donation_categories")
}

model DonationCampaign {
  id            String    @id @default(cuid())
  title         String
  slug          String    @unique
  description   String?   @db.Text
  shortDesc     String?   // Short description for cards
  content       String?   @db.Text // Rich content/story
  target        Float     @default(0)
  collected     Float     @default(0)
  startDate     DateTime  @default(now())
  endDate       DateTime?
  status        String    @default("ACTIVE") // ACTIVE, PAUSED, COMPLETED, CANCELLED
  priority      Int       @default(0) // Higher number = higher priority
  featured      Boolean   @default(false)
  urgent        Boolean   @default(false)
  image         String?   // Main campaign image
  gallery       Json?     // Array of additional images
  videoUrl      String?   // YouTube/Vimeo URL
  location      String?   // Campaign location if applicable
  beneficiaries Int?      // Number of people who will benefit
  tags          Json?     // Array of tags for filtering
  seoTitle      String?
  seoDesc       String?
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  categoryId    String?
  createdById   String
  category      DonationCategory? @relation(fields: [categoryId], references: [id])
  createdBy     User      @relation("CampaignCreator", fields: [createdById], references: [id])
  donations     Donation[]
  updates       CampaignUpdate[]

  @@index([categoryId], map: "donation_campaigns_categoryId_fkey")
  @@index([createdById], map: "donation_campaigns_createdById_fkey")
  @@index([status], map: "donation_campaigns_status_idx")
  @@index([featured], map: "donation_campaigns_featured_idx")
  @@map("donation_campaigns")
}

model CampaignUpdate {
  id          String    @id @default(cuid())
  title       String
  content     String    @db.Text
  image       String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  campaignId  String
  createdById String
  campaign    DonationCampaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  createdBy   User      @relation("CampaignUpdateCreator", fields: [createdById], references: [id])

  @@index([campaignId], map: "campaign_updates_campaignId_fkey")
  @@index([createdById], map: "campaign_updates_createdById_fkey")
  @@map("campaign_updates")
}

model Donation {
  id            String    @id @default(cuid())
  amount        Float
  donorName     String
  donorEmail    String?
  donorPhone    String?
  purpose       String
  message       String?
  isAnonymous   Boolean   @default(false)
  status        String    @default("PENDING") // PENDING, PAID, CANCELLED, REFUNDED
  paymentMethod String?
  reference     String?   // Payment reference/transaction ID
  confirmedAt   DateTime?
  confirmedBy   String?
  categoryId    String?
  campaignId    String?   // New field for campaign donations
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  category      DonationCategory? @relation(fields: [categoryId], references: [id])
  campaign      DonationCampaign? @relation(fields: [campaignId], references: [id])
  confirmer     User?     @relation("DonationConfirmer", fields: [confirmedBy], references: [id])

  @@index([categoryId], map: "donations_categoryId_fkey")
  @@index([campaignId], map: "donations_campaignId_fkey")
  @@index([confirmedBy], map: "donations_confirmedBy_fkey")
  @@index([status], map: "donations_status_idx")
  @@map("donations")
}

model Subscription {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String?
  user      User?    @relation(fields: [userId], references: [id])

  @@index([userId], map: "subscriptions_userId_fkey")
  @@map("subscriptions")
}

model SiteSettings {
  id                String   @id @default(cuid())
  siteName          String   @default("Rumah Tahfidz Baitus Shuffah")
  siteDescription   String   @default("Sistem Informasi Rumah Tahfidz")
  contactEmail      String   @default("<EMAIL>")
  contactPhone      String   @default("+62123456789")
  address           String   @default("Jl. Contoh No. 123, Jakarta")
  socialFacebook    String?
  socialInstagram   String?
  socialTwitter     String?
  socialYoutube     String?
  logoUrl           String?
  faviconUrl        String?
  maintenanceMode   Boolean  @default(false)
  registrationOpen  Boolean  @default(true)
  paymentGateway    String   @default("MANUAL")
  paymentApiKey     String?
  whatsappApiKey    String?
  whatsappInstance  String?
  emailSmtpHost     String?
  emailSmtpPort     Int?
  emailSmtpUser     String?
  emailSmtpPass     String?
  emailFromAddress  String?
  emailFromName     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("site_settings")
}

model SiteSetting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  type        String   @default("STRING") // STRING, BOOLEAN, NUMBER, JSON, IMAGE
  category    String   @default("GENERAL") // GENERAL, SYSTEM, CONTACT, ABOUT, INTEGRATION, APPEARANCE
  label       String
  description String?
  isPublic    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("site_setting")
}

model Theme {
  id        String   @id @default(cuid())
  name      String
  colors    String
  buttons   String
  fonts     String
  layout    String
  logo      String
  isActive  Boolean  @default(false)
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId], map: "theme_userId_fkey")
  @@map("themes")
}

model Behavior {
  id              String    @id @default(cuid())
  santriId        String
  halaqahId       String?
  criteriaId      String
  criteriaName    String
  category        String    // AKHLAQ, IBADAH, SOSIAL, AKADEMIK
  type            String    // POSITIVE, NEGATIVE
  severity        String    @default("LOW") // LOW, MEDIUM, HIGH
  points          Int       @default(0)
  date            DateTime  @default(now())
  time            String?
  description     String
  context         String?
  location        String?
  status          String    @default("ACTIVE") // ACTIVE, RESOLVED, ARCHIVED
  recordedBy      String
  recordedAt      DateTime  @default(now())
  followUpRequired Boolean  @default(false)
  parentNotified  Boolean   @default(false)
  parentNotifiedAt DateTime?
  metadata        Json?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  santri          Santri    @relation(fields: [santriId], references: [id], onDelete: Cascade)
  halaqah         Halaqah?  @relation(fields: [halaqahId], references: [id])
  recordedByUser  User      @relation("BehaviorRecorder", fields: [recordedBy], references: [id])

  @@index([santriId], map: "behavior_santriId_fkey")
  @@index([halaqahId], map: "behavior_halaqahId_fkey")
  @@index([recordedBy], map: "behavior_recordedBy_fkey")
  @@map("behavior")
}

model PaymentGateway {
  id            String   @id @default(cuid())
  name          String   @unique
  type          String   // BANK_TRANSFER, E_WALLET, QRIS, VIRTUAL_ACCOUNT, CREDIT_CARD
  provider      String   // MIDTRANS, XENDIT, DOKU, etc.
  isActive      Boolean  @default(true)
  config        Json     // Configuration data (API keys, etc.)
  fees          Json     // Fee structure
  description   String?  // Description of the payment method
  logo          String?  // Logo URL
  sortOrder     Int      @default(0) // For ordering in UI
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  transactions  Transaction[] @relation("PaymentGatewayTransactions")

  @@map("payment_gateways")
}

model BankAccount {
  id            String   @id @default(cuid())
  bankName      String   // Nama Bank (BCA, BNI, BRI, etc.)
  accountNumber String   // Nomor Rekening
  accountName   String   // Nama Pemilik Rekening
  branch        String?  // Cabang Bank
  isActive      Boolean  @default(true)
  isDefault     Boolean  @default(false) // Rekening utama
  description   String?  // Keterangan tambahan
  logo          String?  // Logo bank
  sortOrder     Int      @default(0)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  transactions  Transaction[] @relation("BankAccountTransactions")

  @@map("bank_accounts")
}

// ===== SALARY & WALLET SYSTEM MODELS =====

model MusyrifSalaryRate {
  id            String   @id @default(cuid())
  musyrifId     String
  ratePerSession Decimal @db.Decimal(10, 2)
  ratePerHour   Decimal  @db.Decimal(10, 2)
  effectiveDate DateTime @db.Date
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  musyrif       Musyrif  @relation("MusyrifSalaryRates", fields: [musyrifId], references: [id], onDelete: Cascade)

  @@map("musyrif_salary_rates")
}

model MusyrifWallet {
  id              String   @id @default(cuid())
  musyrifId       String   @unique
  balance         Decimal  @default(0.00) @db.Decimal(12, 2)
  totalEarned     Decimal  @default(0.00) @db.Decimal(12, 2)
  totalWithdrawn  Decimal  @default(0.00) @db.Decimal(12, 2)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  musyrif         Musyrif  @relation("MusyrifWallet", fields: [musyrifId], references: [id], onDelete: Cascade)
  earnings        MusyrifEarning[]
  withdrawals     MusyrifWithdrawal[]

  @@map("musyrif_wallets")
}

model MusyrifEarning {
  id               String   @id @default(cuid())
  musyrifId        String
  attendanceId     String
  amount           Decimal  @db.Decimal(10, 2)
  calculationType  CalculationType
  sessionDuration  Int?     // dalam menit
  rate             Decimal  @db.Decimal(10, 2)
  status           EarningStatus @default(PENDING)
  approvedBy       String?
  approvedAt       DateTime?
  notes            String?  @db.Text
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  musyrif          Musyrif  @relation("MusyrifEarnings", fields: [musyrifId], references: [id], onDelete: Cascade, map: "earnings_musyrif_fkey")
  attendance       MusyrifAttendance @relation("AttendanceEarnings", fields: [attendanceId], references: [id], onDelete: Cascade, map: "earnings_attendance_fkey")
  approver         User?    @relation("EarningApprover", fields: [approvedBy], references: [id], map: "earnings_approver_fkey")
  wallet           MusyrifWallet @relation(fields: [musyrifId], references: [musyrifId], map: "earnings_wallet_fkey")

  @@map("musyrif_earnings")
}

model MusyrifWithdrawal {
  id              String   @id @default(cuid())
  musyrifId       String
  amount          Decimal  @db.Decimal(10, 2)
  bankAccount     String
  bankName        String
  accountHolder   String
  status          WithdrawalStatus @default(PENDING)
  requestedAt     DateTime @default(now())
  approvedBy      String?
  approvedAt      DateTime?
  completedAt     DateTime?
  rejectionReason String?  @db.Text
  notes           String?  @db.Text

  // Relations
  musyrif         Musyrif  @relation("MusyrifWithdrawals", fields: [musyrifId], references: [id], onDelete: Cascade, map: "withdrawals_musyrif_fkey")
  approver        User?    @relation("WithdrawalApprover", fields: [approvedBy], references: [id], map: "withdrawals_approver_fkey")
  wallet          MusyrifWallet @relation(fields: [musyrifId], references: [musyrifId], map: "withdrawals_wallet_fkey")

  @@map("musyrif_withdrawals")
}

// ===== ENUMS FOR SALARY SYSTEM =====

enum CalculationType {
  PER_SESSION
  PER_HOUR
}

enum EarningStatus {
  PENDING
  APPROVED
  REJECTED
}

enum WithdrawalStatus {
  PENDING
  APPROVED
  REJECTED
  COMPLETED
}
