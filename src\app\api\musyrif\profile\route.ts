import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

// Get musyrif profile
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(request.url);

    console.log("Session:", session ? "exists" : "null");
    console.log("Session user:", session?.user ? session.user.id : "null");
    console.log("Query musyrifId:", searchParams.get("musyrifId"));

    let musyrifId: string;

    if (searchParams.get("musyrifId")) {
      musyrifId = searchParams.get("musyrifId")!;
      console.log("Using query musyrifId:", musyrifId);
    } else {
      // Always fallback to test user for testing purposes
      console.log("Looking for test user...");
      const testUser = await prisma.user.findFirst({
        where: {
          email: "<EMAIL>",
          role: "MUSYRIF"
        }
      });

      console.log("Test user found:", testUser ? testUser.id : "Not found");

      if (testUser) {
        musyrifId = testUser.id;
        console.log("Using test user ID:", musyrifId);
      } else if (session?.user?.id) {
        musyrifId = session.user.id;
        console.log("Using session user ID:", musyrifId);
      } else {
        return NextResponse.json(
          { success: false, message: "Unauthorized - No valid user found" },
          { status: 401 }
        );
      }
    }

    // Verify access - admin can see all, musyrif can only see their own
    // Skip access control for test user
    const isTestUser = musyrifId === "cmdo94xab0000935uxgjilr7k" ||
                      (await prisma.user.findUnique({ where: { id: musyrifId } }))?.email === "<EMAIL>";

    if (!isTestUser && session?.user && session.user.role !== "ADMIN" && musyrifId !== session.user.id) {
      return NextResponse.json(
        { success: false, message: "Access denied" },
        { status: 403 }
      );
    }

    // Get user data with musyrif profile
    const user = await prisma.user.findUnique({
      where: { id: musyrifId },
      include: {
        musyrif: {
          include: {
            halaqah: {
              include: {
                schedules: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: "User tidak ditemukan" },
        { status: 404 }
      );
    }

    if (user.role !== "MUSYRIF") {
      return NextResponse.json(
        { success: false, message: "User bukan musyrif" },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      musyrif: {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        isActive: user.isActive,
        halaqahId: user.halaqahId,
        musyrifProfile: user.musyrif && user.musyrif[0] ? {
          id: user.musyrif[0].id,
          name: user.musyrif[0].name,
          gender: user.musyrif[0].gender,
          birthDate: user.musyrif[0].birthDate,
          birthPlace: user.musyrif[0].birthPlace,
          address: user.musyrif[0].address,
          phone: user.musyrif[0].phone,
          email: user.musyrif[0].email,
          specialization: user.musyrif[0].specialization,
          joinDate: user.musyrif[0].joinDate,
          status: user.musyrif[0].status,
          photo: user.musyrif[0].photo,
          halaqahId: user.musyrif[0].halaqahId,
          halaqah: user.musyrif[0].halaqah ? {
            id: user.musyrif[0].halaqah.id,
            name: user.musyrif[0].halaqah.name,
            description: user.musyrif[0].halaqah.description,
            capacity: user.musyrif[0].halaqah.capacity,
            level: user.musyrif[0].halaqah.level,
            room: user.musyrif[0].halaqah.room,
            status: user.musyrif[0].halaqah.status,
            schedules: user.musyrif[0].halaqah.schedules.map(schedule => ({
              id: schedule.id,
              dayOfWeek: schedule.dayOfWeek,
              startTime: schedule.startTime,
              endTime: schedule.endTime,
              room: schedule.room,
            })),
          } : null,
        } : null,
      },
      message: "Profil musyrif berhasil dimuat",
    });
  } catch (error) {
    console.error("Error fetching musyrif profile:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Gagal memuat profil musyrif",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// Update musyrif profile
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { musyrifId, ...updateData } = body;
    const targetId = musyrifId || session.user.id;

    // Verify access - admin can update all, musyrif can only update their own
    if (session.user.role !== "ADMIN" && targetId !== session.user.id) {
      return NextResponse.json(
        { success: false, message: "Access denied" },
        { status: 403 }
      );
    }

    // Update user data
    const updatedUser = await prisma.user.update({
      where: { id: targetId },
      data: {
        name: updateData.name,
        email: updateData.email,
        phone: updateData.phone,
      },
      include: {
        musyrifProfile: {
          include: {
            halaqah: true,
          },
        },
      },
    });

    // Update musyrif profile if exists
    if (updatedUser.musyrifProfile && updateData.musyrifProfile) {
      await prisma.musyrif.update({
        where: { id: updatedUser.musyrifProfile.id },
        data: {
          name: updateData.musyrifProfile.name || updateData.name,
          gender: updateData.musyrifProfile.gender,
          birthDate: updateData.musyrifProfile.birthDate ? new Date(updateData.musyrifProfile.birthDate) : undefined,
          birthPlace: updateData.musyrifProfile.birthPlace,
          address: updateData.musyrifProfile.address,
          phone: updateData.musyrifProfile.phone || updateData.phone,
          email: updateData.musyrifProfile.email || updateData.email,
          specialization: updateData.musyrifProfile.specialization,
          photo: updateData.musyrifProfile.photo,
        },
      });
    }

    return NextResponse.json({
      success: true,
      message: "Profil berhasil diperbarui",
    });
  } catch (error) {
    console.error("Error updating musyrif profile:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Gagal memperbarui profil",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
