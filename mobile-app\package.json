{"name": "tpq-wali-app", "version": "1.0.0", "description": "TPQ Bai<PERSON> - Aplikasi Wali <PERSON>ri", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:apk": "node scripts/build-apk.js", "build:preview": "eas build --platform android --profile preview", "build:production": "eas build --platform android --profile production", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "setup:build": "node scripts/setup-build.js", "generate:assets": "node scripts/generate-assets.js", "build:status": "eas build:list", "prebuild": "expo prebuild --clean", "env:create": "node scripts/manage-env.js create", "env:validate": "node scripts/manage-env.js validate", "env:configure": "node scripts/manage-env.js configure", "env:status": "node scripts/manage-env.js status", "upgrade:sdk": "node scripts/upgrade-sdk.js", "doctor": "npx expo doctor", "check:sdk": "npx expo config --type public", "fix:turbo": "node scripts/fix-turbo-module.js", "fix:expo-go": "node scripts/fix-turbo-module.js expo-go", "fix:restore": "node scripts/fix-turbo-module.js restore", "apk:generate": "node scripts/auto-apk-generator.js", "apk:auto": "node scripts/auto-apk-generator.js auto", "apk:history": "node scripts/auto-apk-generator.js history", "apk:config": "node scripts/auto-apk-generator.js config", "schedule:start": "node scripts/apk-scheduler.js start", "schedule:status": "node scripts/apk-scheduler.js status", "schedule:enable": "node scripts/apk-scheduler.js enable", "schedule:disable": "node scripts/apk-scheduler.js disable", "schedule:test": "node scripts/apk-scheduler.js test", "notify:test": "node scripts/apk-notifier.js test", "notify:config": "node scripts/apk-notifier.js config", "apk:demo": "node scripts/demo-apk-generator.js", "admin:start": "node scripts/admin-apk-service.js", "admin:dev": "nodemon scripts/admin-apk-service.js"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "axios": "^1.6.5", "cors": "^2.8.5", "date-fns": "^3.2.0", "expo": "~53.0.0", "expo-blur": "~14.1.5", "expo-device": "^7.1.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-notifications": "~0.31.4", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.2.0", "express": "^5.1.0", "multer": "^2.0.2", "node-cron": "^4.2.1", "nodemon": "^3.1.10", "react": "18.3.1", "react-hook-form": "^7.49.3", "react-native": "0.76.5", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "~2.16.1", "react-native-modal": "^13.0.1", "react-native-paper": "^5.12.3", "react-native-progress": "^5.0.1", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "~3.31.1", "react-native-skeleton-placeholder": "^5.2.4", "react-native-super-grid": "^5.0.0", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-query": "^3.39.3", "ws": "^8.18.3", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.3.12", "typescript": "^5.1.3"}, "private": true}