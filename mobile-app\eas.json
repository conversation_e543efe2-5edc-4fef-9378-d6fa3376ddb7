{"cli": {"version": ">= 12.0.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "env": {"NODE_ENV": "development"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "env": {"NODE_ENV": "production"}}, "production": {"android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "env": {"NODE_ENV": "production"}}, "production-aab": {"android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}, "env": {"NODE_ENV": "production"}}}, "submit": {"production": {"android": {"serviceAccountKeyPath": "../path/to/api-key.json", "track": "internal"}}}}