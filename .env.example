# Database Configuration
DATABASE_URL="mysql://username:password@localhost:3306/tpq_baitus_shuffah"

# Next.js Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key-here"

# Application Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="TPQ Baitus Shuffah"

# Payment Gateway - Midtrans
MIDTRANS_SERVER_KEY="your-midtrans-server-key"
MIDTRANS_CLIENT_KEY="your-midtrans-client-key"
MIDTRANS_IS_PRODUCTION="false"

# File Upload - Cloudinary
CLOUDINARY_CLOUD_NAME="your-cloudinary-cloud-name"
CLOUDINARY_API_KEY="your-cloudinary-api-key"
CLOUDINARY_API_SECRET="your-cloudinary-api-secret"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
SMTP_FROM="<EMAIL>"

# WhatsApp Integration
WHATSAPP_API_URL="https://api.whatsapp.com"
WHATSAPP_TOKEN="your-whatsapp-token"
WHATSAPP_PHONE_NUMBER_ID="your-phone-number-id"

# Development/Debug
NODE_ENV="development"
DEBUG="false"
LOG_LEVEL="info"

# Security
JWT_SECRET="your-jwt-secret-key-here"
ENCRYPTION_KEY="your-encryption-key-here"

# Redis (Optional - for caching)
REDIS_URL="redis://localhost:6379"

# Analytics (Optional)
GOOGLE_ANALYTICS_ID="your-ga-id"

# Backup Configuration
BACKUP_ENABLED="true"
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS="30"
