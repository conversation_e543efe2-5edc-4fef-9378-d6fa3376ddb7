import midtransClient from "midtrans-client";

// Check if Midtrans keys are configured
const isMidtransConfigured =
  process.env.MIDTRANS_SERVER_KEY &&
  process.env.MIDTRANS_SERVER_KEY !== "your-midtrans-server-key" &&
  process.env.MIDTRANS_CLIENT_KEY &&
  process.env.MIDTRANS_CLIENT_KEY !== "your-midtrans-client-key";

// Initialize Snap API if configured
let snap: midtransClient.Snap | null = null;
let coreApi: midtransClient.CoreApi | null = null;

try {
  if (isMidtransConfigured) {
    snap = new midtransClient.Snap({
      isProduction: process.env.MIDTRANS_IS_PRODUCTION === "true",
      serverKey: process.env.MIDTRANS_SERVER_KEY!,
      clientKey: process.env.MIDTRANS_CLIENT_KEY!,
    });

    coreApi = new midtransClient.CoreApi({
      isProduction: process.env.MIDTRANS_IS_PRODUCTION === "true",
      serverKey: process.env.MIDTRANS_SERVER_KEY!,
      clientKey: process.env.MIDTRANS_CLIENT_KEY!,
    });
  }
} catch (error) {
  console.error("Failed to initialize Midtrans client:", error);
  snap = null;
  coreApi = null;
}

export { snap, coreApi };

// Payment types
export interface PaymentRequest {
  orderId: string;
  amount: number;
  customerDetails: {
    firstName: string;
    lastName?: string;
    email: string;
    phone: string;
  };
  itemDetails: Array<{
    id: string;
    price: number;
    quantity: number;
    name: string;
  }>;
  customExpiry?: {
    expiry_duration: number;
    unit: "second" | "minute" | "hour" | "day";
  };
}

// Create payment token for SPP
export async function createSPPPayment(paymentData: PaymentRequest) {
  try {
    // Check if Midtrans is configured
    if (!isMidtransConfigured || !snap) {
      console.warn("Midtrans is not configured. Using development mode.");

      // Return mock data for development
      return {
        success: true,
        token: "mock-token-" + Date.now(),
        redirectUrl: `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/payment/success?order_id=${paymentData.orderId}`,
      };
    }

    const parameter = {
      transaction_details: {
        order_id: paymentData.orderId,
        gross_amount: paymentData.amount,
      },
      customer_details: paymentData.customerDetails,
      item_details: paymentData.itemDetails,
      credit_card: {
        secure: true,
      },
      custom_expiry: paymentData.customExpiry || {
        expiry_duration: 24,
        unit: "hour",
      },
      callbacks: {
        finish: `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/payment/success`,
        error: `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/payment/error`,
        pending: `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/payment/pending`,
      },
    };

    const transaction = await snap.createTransaction(parameter);
    return {
      success: true,
      token: transaction.token,
      redirectUrl: transaction.redirect_url,
    };
  } catch (error) {
    console.error("Midtrans SPP Payment Error:", error);
    return {
      success: false,
      error: "Failed to create payment",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Create payment token for Donation
export async function createDonationPayment(paymentData: PaymentRequest) {
  try {
    // Check if Midtrans is configured
    if (!isMidtransConfigured || !snap) {
      console.warn("Midtrans is not configured. Using development mode.");

      // Return mock data for development with a clear message
      return {
        success: true,
        token: "mock-token-" + Date.now(),
        redirectUrl: `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/donation/success?order_id=${paymentData.orderId}&dev_mode=true`,
        devMode: true,
      };
    }

    // Validate required fields
    if (!paymentData.orderId) {
      return {
        success: false,
        error: "Missing order ID",
        details: "Order ID is required for payment creation",
      };
    }

    if (!paymentData.amount || paymentData.amount < 1) {
      return {
        success: false,
        error: "Invalid amount",
        details: "Amount must be greater than 0",
      };
    }

    if (
      !paymentData.customerDetails ||
      !paymentData.customerDetails.firstName
    ) {
      return {
        success: false,
        error: "Missing customer details",
        details: "Customer name is required",
      };
    }

    // Ensure item details are valid
    if (!paymentData.itemDetails || paymentData.itemDetails.length === 0) {
      return {
        success: false,
        error: "Missing item details",
        details: "At least one item is required",
      };
    }

    // Create parameter object for Midtrans
    const parameter = {
      transaction_details: {
        order_id: paymentData.orderId,
        gross_amount: paymentData.amount,
      },
      customer_details: {
        first_name: paymentData.customerDetails.firstName,
        email: paymentData.customerDetails.email || "<EMAIL>",
        phone: paymentData.customerDetails.phone || "08123456789",
      },
      item_details: paymentData.itemDetails.map((item) => ({
        id: item.id,
        price: item.price,
        quantity: item.quantity,
        name: item.name,
      })),
      credit_card: {
        secure: true,
      },
      custom_expiry: paymentData.customExpiry || {
        expiry_duration: 1,
        unit: "hour",
      },
      callbacks: {
        finish: `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/donation/success`,
        error: `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/donation/error`,
        pending: `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/donation/pending`,
      },
    };

    console.log("Midtrans parameter:", JSON.stringify(parameter, null, 2));

    try {
      if (!snap) {
        console.warn("Snap client is not initialized, using development mode");
        return {
          success: false, // Return false on transaction creation error
          token: "mock-token-" + Date.now(),
          redirectUrl: `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/donation/error?order_id=${paymentData.orderId}&dev_mode=true`, // Redirect to error page
          devMode: true,
        };
      }

      const transaction = await snap.createTransaction(parameter);
      console.log("Midtrans transaction created:", transaction);

      if (!transaction || !transaction.token) {
        console.error("Invalid transaction response from Midtrans");
        return {
          success: false, // Return false on invalid transaction response
          token: "mock-token-" + Date.now(), // Still provide a mock token for consistency, but success is false
          redirectUrl: `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/donation/error?order_id=${paymentData.orderId}&dev_mode=true`, // Redirect to error page
          devMode: true,
          error: "Invalid response from payment gateway",
        };
      }

      return {
        success: true,
        token: transaction.token,
        redirectUrl: transaction.redirect_url,
      };
    } catch (transactionError) {
      console.error("Midtrans transaction creation error:", transactionError);
      // Return success with dev mode to avoid breaking the client
      return {
        success: true,
        token: "mock-token-" + Date.now(),
        redirectUrl: `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/donation/success?order_id=${paymentData.orderId}&dev_mode=true`,
        devMode: true,
        error:
          "Failed to create transaction with payment gateway, using development mode",
        details:
          transactionError instanceof Error
            ? transactionError.message
            : "Unknown transaction error",
      };
    }
  } catch (error) {
    console.error("Midtrans Donation Payment Error:", error);
    return {
      success: false,
      error: "Failed to create donation payment",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Check payment status
export async function checkPaymentStatus(orderId: string) {
  try {
    // Check if Midtrans is configured
    if (!isMidtransConfigured || !coreApi) {
      console.warn("Midtrans is not configured. Using development mode.");

      // Return mock data for development
      return {
        success: true,
        data: {
          transaction_status: "settlement",
          order_id: orderId,
          payment_type: "credit_card",
          gross_amount: "10000.00",
          transaction_time: new Date().toISOString(),
        },
      };
    }

    const statusResponse = await coreApi.transaction.status(orderId);
    return {
      success: true,
      data: statusResponse,
    };
  } catch (error) {
    console.error("Midtrans Status Check Error:", error);
    return {
      success: false,
      error: "Failed to check payment status",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Cancel payment
export async function cancelPayment(orderId: string) {
  try {
    // Check if Midtrans is configured
    if (!isMidtransConfigured || !coreApi) {
      console.warn("Midtrans is not configured. Using development mode.");

      // Return mock data for development
      return {
        success: true,
        data: {
          transaction_status: "cancel",
          order_id: orderId,
          payment_type: "credit_card",
          gross_amount: "10000.00",
          transaction_time: new Date().toISOString(),
        },
      };
    }

    const cancelResponse = await coreApi.transaction.cancel(orderId);
    return {
      success: true,
      data: cancelResponse,
    };
  } catch (error) {
    console.error("Midtrans Cancel Payment Error:", error);
    return {
      success: false,
      error: "Failed to cancel payment",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Verify webhook signature
export function verifyWebhookSignature(
  orderId: string,
  statusCode: string,
  grossAmount: string,
  serverKey: string,
  signatureKey: string,
): boolean {
  const crypto = require("crypto");
  const hash = crypto
    .createHash("sha512")
    .update(orderId + statusCode + grossAmount + serverKey)
    .digest("hex");

  return hash === signatureKey;
}

// Payment status mapping
export const PAYMENT_STATUS = {
  PENDING: "pending",
  SUCCESS: "settlement",
  FAILED: "failure",
  CANCELLED: "cancel",
  EXPIRED: "expire",
} as const;

// Payment method mapping
export const PAYMENT_METHODS = {
  CREDIT_CARD: "credit_card",
  BANK_TRANSFER: "bank_transfer",
  E_WALLET: "e_wallet",
  QRIS: "qris",
  CONVENIENCE_STORE: "cstore",
} as const;
